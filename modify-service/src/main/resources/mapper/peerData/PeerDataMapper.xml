<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.peer.mapper.PeerDataMapper">


    <select id="getPeerTableMarketFieldNullValueData" resultType="PeerDataFixDTO">
        SELECT h.sec_code,
               h.sec_name,
               replace(replace(h.sec_name, 'N', ''), 'XD', '') as processed_sec_name,
               si.sec_name                                     as exchange_sec_name,
               si.source                                       as
                                                                  market,
               si.sec_type
        from (SELECT sec_code, sec_name
              from ${schemaName}.${peerTableName}
              WHERE market is null
                and sec_name is not null
                and if_market_checked = 1
              GROUP BY sec_code, sec_name
              ORDER BY sec_code, sec_name) h
                 INNER JOIN
             credit.t_rzrq_sec_info si
             on h.sec_code = si.`code`;
    </select>

    <update id="updateMarketField">
        UPDATE ${schemaName}.${tableName}
        SET market            = #{bestMatchRecord.market,jdbcType=VARCHAR},
            if_market_checked = 0
        where sec_code = #{bestMatchRecord.secCode,jdbcType=VARCHAR}
          and sec_name = #{bestMatchRecord.secName,jdbcType=VARCHAR}
          and if_market_checked in (1,2)
    </update>

    <select id="getPeerTableMarketFieldNonDataByFundMethod" resultType="PeerDataFixDTO">
        SELECT h.sec_code,
               h.sec_name,
               replace(replace(h.sec_name, 'N', ''), 'XD', '') as processed_sec_name,
               si.sec_name                                     as exchange_sec_name,
               pl.sec_name                                     as exchange_sec_name2,
               si.source                                       as market,
               fd.full_name                                    as fund_full_name,
               fd.sec_name                                     as
                                                                  fund_name
        from (SELECT sec_code, sec_name
              from ${schemaName}.${tableName}
              WHERE market is null
                and sec_name is not null
              GROUP BY sec_code, sec_name
              ORDER BY sec_code, sec_name) h
                 INNER JOIN
             credit.t_rzrq_sec_info si
             on h.sec_code = si.`code`
                 INNER JOIN
             credit.t_jys_product_list pl
             on h.sec_code = pl.code
                 INNER JOIN
             credit.t_fund_archives fd
             on h.sec_code = fd.sec_code
        WHERE si.sec_type = 1;
    </select>

    <select id="getPeerTableMarketFieldNonCheckedByFundMethod" resultType="PeerDataFixDTO">
        SELECT h.sec_code,
               h.sec_name,
               replace(replace(h.sec_name, 'N', ''), 'XD', '') as processed_sec_name,
               si.sec_name                                     as exchange_sec_name,
               pl.sec_name                                     as exchange_sec_name2,
               si.source                                       as market,
               fd.full_name                                    as fund_full_name,
               fd.sec_name                                     as
                                                                  fund_name
        from (SELECT sec_code, sec_name
              from ${schemaName}.${tableName}
              WHERE if_market_checked = 1
                and sec_name is not null
              GROUP BY sec_code, sec_name
              ORDER BY sec_code, sec_name) h
                 INNER JOIN
             credit.t_rzrq_sec_info si
             on h.sec_code = si.`code`
                 INNER JOIN
             credit.t_jys_product_list pl
             on h.sec_code = pl.code
                 INNER JOIN
             credit.t_fund_archives fd
             on h.sec_code = fd.sec_code
        WHERE si.sec_type = 1;
    </select>

    <insert id="addPeerTableEnableStatusField">
        ALTER TABLE ${schemaName}.${tableName}
            ADD COLUMN `${tableFieldName}` TINYINT(1) UNSIGNED ZEROFILL NOT NULL DEFAULT 0 COMMENT '启用状态：0启用，1禁用';
    </insert>

    <select id="selectPeerTableWithoutDesignateField" resultType="java.lang.String">
        SELECT t.table_name
        FROM information_schema.tables AS t
                 LEFT JOIN information_schema.columns AS c
                           ON t.table_name = c.table_name
                               AND t.table_schema = #{schemaName,jdbcType=VARCHAR}
                               AND c.column_name = #{tableFieldName,jdbcType=VARCHAR}
                 INNER JOIN
             credit.t_finance_settings fs
             on t.TABLE_NAME like concat('%', fs.en_name, '%') and fs.mark2 = 1
        WHERE t.table_schema = #{schemaName,jdbcType=VARCHAR}
          AND c.column_name IS NULL
          and t.TABLE_NAME like '%t_rzrq_%_%'
          and t.TABLE_NAME not like '%copy%'
          and (t.TABLE_NAME like '%zsl' or t.TABLE_NAME like '%bdzq' or t.TABLE_NAME like '%jzd');
    </select>

    <select id="selectPeerTableWithoutIfMarketCheckedField" resultType="java.lang.String">
        SELECT t.table_name
        FROM information_schema.tables AS t
                 LEFT JOIN information_schema.columns AS c
                           ON t.table_name = c.table_name
                               AND t.table_schema = #{schemaName,jdbcType=VARCHAR}
                               AND c.column_name = 'if_market_checked'
                 INNER JOIN
             credit.t_finance_settings fs
             on t.TABLE_NAME like concat('%', fs.en_name, '%') and fs.mark2 = 1
        WHERE t.table_schema = #{schemaName,jdbcType=VARCHAR}
          AND c.column_name IS NULL
          and t.TABLE_NAME like '%t_rzrq_%_%'
          and t.TABLE_NAME not like '%copy%';
    </select>

    <insert id="addPeerTableIfMarketCheckedField">
        ALTER TABLE ${schemaName}.${tableName}
            ADD COLUMN `${tableFieldName}` TINYINT(1) UNSIGNED ZEROFILL NOT NULL DEFAULT '1' COMMENT
        '是否已检查market字段：0已检查，1未检查';
    </insert>

    <select id="selectValidPeerSecuritiesTableNameList" resultType="java.lang.String">
        SELECT t.table_name
        FROM information_schema.tables AS t
                 INNER JOIN
             credit.t_finance_settings fs
             on t.TABLE_NAME like concat('%', fs.en_name, '%') and fs.mark2 = 1
        WHERE t.table_schema = 'credit'
          and t.TABLE_NAME like '%t_rzrq_%_%'
          and t.TABLE_NAME not like '%_tz'
          and t.TABLE_NAME not like '%copy%'
          and (t.TABLE_NAME like '%zsl' or t.TABLE_NAME like '%bdzq' or t.TABLE_NAME like '%jzd');
    </select>

    <insert id="insertStockTypeWrongRecordToErrorTable">
        INSERT ignore into credit.t_rzrq_market_error_record(sec_code, sec_name, sse_date, market, source, table_name)

        SELECT z.sec_code, z.sec_name, z.sse_date, z.market, z.source, #{tableName,jdbcType=VARCHAR} as table_name
        from credit.${tableName} z
                 INNER JOIN
             credit.t_rzrq_sec_info si
             on z.sec_code = si.`code` and si.sec_type = 0
        WHERE (
        z.market != si.source
        <if test="ifThisTableMarketFieldNullValueTable == false">
            or z.market is null
        </if>
        )
          and z.if_market_checked in (1, 2)
          and z.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP}

        UNION
        SELECT z.sec_code, z.sec_name, z.sse_date, z.market, z.source, #{tableName,jdbcType=VARCHAR} as table_name
        from credit.${tableName} z
                 INNER JOIN
             credit.t_rzrq_sec_info si
             on z.sec_code = si.`code` and z.sec_name = si.sec_name and si.sec_type in (1, 2)
        WHERE (
        z.market != si.source
        <if test="ifThisTableMarketFieldNullValueTable == false">
            or z.market is null
        </if>
        )
          and z.if_market_checked in (1, 2)
          and z.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP}

        UNION
        SELECT z.sec_code, z.sec_name, z.sse_date, z.market, z.source, #{tableName,jdbcType=VARCHAR} as table_name
        from credit.${tableName} z
                 INNER JOIN
             credit.t_jys_product_list si
             on z.sec_code = si.`code` and z.sec_name = si.sec_name and si.sec_type in (1, 2)
        WHERE (
        z.market != si.source
        <if test="ifThisTableMarketFieldNullValueTable == false">
            or z.market is null
        </if>
        )
          and z.if_market_checked in (1, 2)
          and z.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP}
        ORDER BY sse_date desc;
    </insert>

    <update id="fixMarketFieldErrorValueByStockType">
        update
            credit.t_rzrq_sec_info si ,
            credit.${tableName} z
        set z.market            = si.source,
            z.if_market_checked = 0
        WHERE si.`code` = z.sec_code
          and si.sec_type = 0
          and z.if_market_checked in (1,2) and z.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP} ;
    </update>

    <insert id="fixMarketFieldErrorValueByStockTypeNew">
        INSERT into credit.t_peer_mrg_trd_market_temp(table_data_id,market,table_name)

        SELECT z.id,si.source,#{tableName,jdbcType=VARCHAR} as table_name
        FROM
            credit.t_rzrq_sec_info si
                INNER JOIN credit.${tableName} as z
        WHERE
            si.`code` = z.sec_code
          AND si.sec_type = 0
          AND z.if_market_checked IN ( 1, 2 )
          AND z.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP} ;
    </insert>

    <select id="getPeerTableMarketFieldNonCheckRecordCount" resultType="java.lang.Integer">
        SELECT count(0)
        from credit.${tableName} d
        WHERE d.if_market_checked = 1;
    </select>

    <update id="updateMatchedFundOrBondTypeMarketField">
        UPDATE credit.${tableName} h
            JOIN credit.t_rzrq_sec_info si
            ON h.sec_code = si.`code`
        SET h.market            = si.source,
            h.if_market_checked = 0
        WHERE (
            replace(REPLACE(REPLACE(h.sec_name, 'XD', ''), 'N', ''), ' ', '') LIKE CONCAT('%', si.sec_name, '%')
                OR si.sec_name LIKE CONCAT('%', replace(REPLACE(REPLACE(h.sec_name, 'XD', ''), 'N', ''), ' ', ''), '%')
            )
          AND si.sec_type IN (1, 2)
          and h.if_market_checked in (1,2) and h.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP};
    </update>

    <update id="updateMatchedFundOrBondTypeMarketFieldNew">
        insert into credit.t_peer_mrg_trd_market_temp(table_data_id,market,table_name)

        SELECT z.id,si.source,#{tableName,jdbcType=VARCHAR} as table_name
        FROM
            credit.t_rzrq_sec_info si
                INNER JOIN credit.${tableName} as z
            on si.code = z.sec_code
        WHERE
            ( replace(REPLACE(REPLACE(z.sec_name, 'XD', ''), 'N', ''), ' ', '') LIKE CONCAT('%', si.sec_name, '%')
                OR si.sec_name LIKE CONCAT('%', replace(REPLACE(REPLACE(z.sec_name, 'XD', ''), 'N', ''), ' ', ''), '%')
            )
          AND si.sec_type IN (1, 2)
          AND z.if_market_checked IN ( 1, 2 )
          AND z.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP} ;

    </update>


    <update id="updateMatchedFundOrBondTypeMarketField2">
        UPDATE credit.${tableName} h
            JOIN credit.t_jys_product_list si
            ON h.sec_code = si.`code`
        SET h.market            = si.source,
            h.if_market_checked = 0
        WHERE (
            replace(REPLACE(REPLACE(h.sec_name, 'XD', ''), 'N', ''), ' ', '') LIKE CONCAT('%', si.sec_name, '%')
                OR si.sec_name LIKE CONCAT('%', replace(REPLACE(REPLACE(h.sec_name, 'XD', ''), 'N', ''), ' ', ''), '%')
            )
          AND si.sec_type IN (1, 2)
          and h.if_market_checked in (1,2) and h.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP};
    </update>

    <select id="getPeerTableMarketFieldNonCheckedData" resultType="PeerDataFixDTO">
        SELECT h.sec_code,
               h.sec_name,
               replace(replace(h.sec_name, 'N', ''), 'XD', '') as processed_sec_name,
               si.sec_name                                     as exchange_sec_name,
               si.source                                       as
                                                                  market,
               si.sec_type
        from (SELECT sec_code, sec_name
              from ${schemaName}.${tableName}
              WHERE if_market_checked in (1,2)
                and sec_name is not null
              GROUP BY sec_code, sec_name
              ORDER BY sec_code, sec_name) h
                 INNER JOIN
             credit.t_rzrq_sec_info si
             on h.sec_code = si.`code`

        UNION
        SELECT h.sec_code,
               h.sec_name,
               replace(replace(h.sec_name, 'N', ''), 'XD', '') as processed_sec_name,
               si.sec_name                                     as exchange_sec_name,
               si.source                                       as
                                                                  market,
               si.sec_type
        from (SELECT sec_code, sec_name
              from ${schemaName}.${tableName}
              WHERE if_market_checked in (1,2)
                and sec_name is not null
              GROUP BY sec_code, sec_name
              ORDER BY sec_code, sec_name) h
                 INNER JOIN
             credit.t_jys_product_list si
             on h.sec_code = si.`code`;
    </select>

    <update id="updateDeListSecurityEnableStatusToDisable">
        update credit.${tableName} h,
            (SELECT h.*
             from (SELECT z.sec_code, z.sec_name, z.market, count(0), max(date) as date
                   from credit.${tableName} z
                   WHERE z.if_market_checked in (1,2)
                   GROUP BY z.sec_code, z.sec_name, z.market
                   ORDER BY date desc) h
                      LEFT JOIN
                  (SELECT si.`code`, si.sec_name
                   from credit.t_rzrq_sec_info si
                   WHERE si.sec_type in (1, 2)
                   UNION
                   SELECT si.`code`, si.sec_name
                   from credit.t_jys_product_list si
                   WHERE si.sec_type in (1, 2)) si
                  on h.sec_code = si.CODE and h.sec_name = si.sec_name
             WHERE si.`code` is null
               and h.date <![CDATA[ < ]]> DATE_SUB(CURRENT_DATE, INTERVAL 2 MONTH)
             order by date desc) i
        set h.if_market_checked = 0,
            h.enable_status     = 1
        WHERE h.sec_code = i.sec_code
          and h.sec_name = i.sec_name and h.if_market_checked in (1,2)
          and h.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP};
    </update>

    <select id="selectPeerTableMaxUpdateTime" resultType="java.time.LocalDateTime">
        select max(update_time) from credit.${tableName} ;
    </select>

    <update id="updateUnmatchedSecurityIfMarketCheckedTo2">
        update credit.${tableName} h
        set h.if_market_checked = 2
        where h.if_market_checked = 1
          and h.update_time <![CDATA[ <= ]]> #{maxDateTime,jdbcType=TIMESTAMP};
    </update>

    <update id="updateMarketFieldByPastDateMatchedRecord">
        UPDATE
            credit.${tableName} i,
            (SELECT z.sec_code,
                    z.sec_name,
                    z.market
             FROM credit.${tableName} z
             WHERE z.enable_status = 0
               AND z.if_market_checked = 0
               and z.sse_date > date_sub(current_date,INTERVAL 7 DAY)
             GROUP BY z.sec_code,
                      z.sec_name) h
        set i.market           = h.market,
            i.if_market_checked=0
        WHERE i.sec_code = h.sec_code
          and i.sec_name = h.sec_name
          and i.if_market_checked = 1
          and i.enable_status = 0;
    </update>

    <insert id="insertOrUpdatePeerHaircutData">
        insert into margin.t_peer_sec_collateral_data(sec_code, sec_name, date, sec_type, ${enName})

        select si.sec_code,
               si.sec_name,
               #{date,jdbcType=DATE},
               si.sec_type,
               case
                   when h.status = 0 then concat(round(if(h.rate > 1, round(h.rate / 100, 2), round(h.rate, 2))), '[受限]')
                   when h.status = 1
                       then concat(if(h.rate > 1, round(h.rate / 100, 2), round(h.rate, 2))) end as ${enName}
        from credit.t_rzrq_sec_info si
                 INNER JOIN
             ${tableName} as h
             on si.code = h.sec_code and si.source = h.market and h.sse_date = #{date,jdbcType=DATE}
                 and h.enable_status = 0 and h.if_market_checked = 0
        where ( select f.zsl_flag
               from credit.t_rzrq_flag f
               where f.date = #{date,jdbcType=VARCHAR}
                 and f.`table` =
                     SUBSTRING_INDEX(#{tableName,jdbcType=VARCHAR}, '.', -1)  ) = 1

        on duplicate key
            update ${enName} =
                       values(${enName})
    </insert>

    <insert id="insertOrUpdatePeerFinanceTargetData">
        insert into margin.t_peer_sec_finance_target_data(sec_code, sec_name, date, sec_type, ${enName})

        select si.sec_code,
               si.sec_name,
               #{date,jdbcType=DATE},
               si.sec_type,
        <choose>
            <when test="enName == 'haitongzq' or enName == 'dfzq' or enName == 'gdzq'">
                '未公开'
            </when>
            <otherwise>
                round(h.rz_rate, 2)
            </otherwise>
        </choose>
        from credit.t_rzrq_sec_info si
                 INNER JOIN
             ${tableName} as h
             on si.code = h.sec_code and si.source = h.market and h.sse_date = #{date,jdbcType=DATE}
                 and h.rzbd = 1 and h.enable_status = 0 and h.if_market_checked = 0
        where (select f.rz_flag
               from credit.t_rzrq_flag f
               where f.date = #{date,jdbcType=VARCHAR}
                 and f.`table` =
                     SUBSTRING_INDEX(#{tableName,jdbcType=VARCHAR}, '.', -1) ) = 1

        on duplicate key update ${enName} = values(${enName})
    </insert>

    <insert id="insertOrUpdatePeerShortSellTargetData">
        insert into margin.t_peer_sec_short_sell_target_data(sec_code, sec_name, date, sec_type, ${enName})

        select si.sec_code,
               si.sec_name,
               #{date,jdbcType=DATE},
               si.sec_type,
        <choose>
            <when test="enName == 'haitongzq' or enName == 'dfzq' or enName == 'gdzq'">
                '未公开'
            </when>
            <otherwise>
                round(h.rq_rate, 2)
            </otherwise>
        </choose>
        from credit.t_rzrq_sec_info si
                 INNER JOIN
             ${tableName} as h
             on si.code = h.sec_code and si.source = h.market and h.sse_date = #{date,jdbcType=DATE}
                 and h.rqbd = 1 and h.enable_status = 0 and h.if_market_checked = 0
        where (select f.rq_flag
               from credit.t_rzrq_flag f
               where f.date = #{date,jdbcType=VARCHAR}
                 and f.`table` =
                    SUBSTRING_INDEX(#{tableName,jdbcType=VARCHAR}, '.', -1)  ) = 1

        on duplicate key update ${enName} = values(${enName})
    </insert>

    <insert id="insertOrUpdatePeerCategoryData">
        insert into margin.t_peer_sec_category_data(sec_code, sec_name, date, sec_type, ${enName})

        select si.sec_code, si.sec_name, #{tradingDate,jdbcType=DATE}, si.sec_type, h.concentration
        from credit.t_rzrq_sec_info si
        INNER JOIN
        ${tableName} as h
        on si.code = h.sec_code and si.source = h.market and h.sse_date = #{tradingDate,jdbcType=DATE}
        where h.concentration is not null
        and h.if_market_checked = 0
        and (select f.c_flag
        from credit.t_rzrq_flag f
        where f.date = #{tradingDate,jdbcType=VARCHAR}
        and f.`table` =
        SUBSTRING_INDEX(#{tableName,jdbcType=VARCHAR}, '.', -1) ) = 1

        on duplicate key update ${enName} = values(${enName})
    </insert>

    <update id="updatePeerCategoryDataNonExist">
        UPDATE margin.t_peer_sec_category_data
        SET
        <foreach collection="categoryNonExistPeerSecurities" item="peer" open="" separator="," close="">
            ${peer} = '无集中度分组'
        </foreach>
        WHERE date = #{date}
    </update>

    <update id="updatePeerAverageHaircutData">
        UPDATE margin.t_peer_sec_collateral_data as t
        JOIN (SELECT `sec_code`,
        `date`,
        ROUND(SUM(
        COALESCE(NULLIF(`zxzq`, ''),
        IF(`zxzq` LIKE '%受限%', NULL, `zxzq`), 0) +
        COALESCE(NULLIF(`zgyh`, ''),
        IF(`zgyh` LIKE '%受限%', NULL, `zgyh`), 0) +
        COALESCE(NULLIF(`gtja`, ''),
        IF(`gtja` LIKE '%受限%', NULL, `gtja`), 0) +
        COALESCE(NULLIF(`htzq`, ''),
        IF(`htzq` LIKE '%受限%', NULL, `htzq`), 0) +
        COALESCE(NULLIF(`zxjt`, ''),
        IF(`zxjt` LIKE '%受限%', NULL, `zxjt`), 0) +
        COALESCE(NULLIF(`zjgs`, ''),
        IF(`zjgs` LIKE '%受限%', NULL, `zjgs`), 0) +
        COALESCE(NULLIF(`haitongzq`, ''),
        IF(`haitongzq` LIKE '%受限%', NULL, `haitongzq`), 0) +
        COALESCE(NULLIF(`swhy`, ''),
        IF(`swhy` LIKE '%受限%', NULL, `swhy`), 0) +
        COALESCE(NULLIF(`gfzq`, ''),
        IF(`gfzq` LIKE '%受限%', NULL, `gfzq`), 0) +
        COALESCE(NULLIF(`zszq`, ''),
        IF(`zszq` LIKE '%受限%', NULL, `zszq`), 0) +
        COALESCE(NULLIF(`gxzq`, ''),
        IF(`gxzq` LIKE '%受限%', NULL, `gxzq`), 0) +
        COALESCE(NULLIF(`dfzq`, ''),
        IF(`dfzq` LIKE '%受限%', NULL, `dfzq`), 0) +
        COALESCE(NULLIF(`zheshangzq`, ''),
        IF(`zheshangzq` LIKE '%受限%', NULL, `zheshangzq`), 0) +
        COALESCE(NULLIF(`gdzq`, ''),
        IF(`gdzq` LIKE '%受限%', NULL, `gdzq`), 0) +
        COALESCE(NULLIF(`dczq`, ''),
        IF(`dczq` LIKE '%受限%', NULL, `dczq`), 0) +
        COALESCE(NULLIF(`tfzq`, ''),
        IF(`tfzq` LIKE '%受限%', NULL, `tfzq`), 0) +
        COALESCE(NULLIF(`xyzq`, ''),
        IF(`xyzq` LIKE '%受限%', NULL, `xyzq`), 0) +
        COALESCE(NULLIF(`ztzq`, ''),
        IF(`ztzq` LIKE '%受限%', NULL, `ztzq`), 0) +
        COALESCE(NULLIF(`xbzq`, ''),
        IF(`xbzq` LIKE '%受限%', NULL, `xbzq`), 0) +
        COALESCE(NULLIF(`cjzq`, ''),
        IF(`cjzq` LIKE '%受限%', NULL, `cjzq`), 0) +
        COALESCE(NULLIF(`gjzq`, ''),
        IF(`gjzq` LIKE '%受限%', NULL, `gjzq`), 0) +
        COALESCE(NULLIF(`dbzq`, ''),
        IF(`dbzq` LIKE '%受限%', NULL, `dbzq`), 0) +
        COALESCE(NULLIF(`gyzq`, ''),
        IF(`gyzq` LIKE '%受限%', NULL, `gyzq`), 0) +
        COALESCE(NULLIF(`ctzq`, ''),
        IF(`ctzq` LIKE '%受限%', NULL, `ctzq`), 0) +
        COALESCE(NULLIF(`dxzq`, ''),
        IF(`dxzq` LIKE '%受限%', NULL, `dxzq`), 0) +
        COALESCE(NULLIF(`cczq`, ''),
        IF(`cczq` LIKE '%受限%', NULL, `cczq`), 0) +
        COALESCE(NULLIF(`hxzq`, ''),
        IF(`hxzq` LIKE '%受限%', NULL, `hxzq`), 0) +
        COALESCE(NULLIF(`zyzq`, ''),
        IF(`zyzq` LIKE '%受限%', NULL, `zyzq`), 0) +
        COALESCE(NULLIF(`njzq`, ''),
        IF(`njzq` LIKE '%受限%', NULL, `njzq`), 0) +
        COALESCE(NULLIF(`xnzq`, ''),
        IF(`xnzq` LIKE '%受限%', NULL, `xnzq`), 0) +
        COALESCE(NULLIF(`pazq`, ''),
        IF(`pazq` LIKE '%受限%', NULL, `pazq`), 0) +
        COALESCE(NULLIF(`axzq`, ''),
        IF(`axzq` LIKE '%受限%', NULL, `axzq`), 0)
        ) /
        SUM(
        (NULLIF(`zxzq`, '') IS NOT NULL AND `zxzq` not like '%受限%') +
        (NULLIF(`zgyh`, '') IS NOT NULL AND `zgyh` not like '%受限%') +
        (NULLIF(`gtja`, '') IS NOT NULL AND `gtja` not like '%受限%') +
        (NULLIF(`htzq`, '') IS NOT NULL AND `htzq` not like '%受限%') +
        (NULLIF(`zxjt`, '') IS NOT NULL AND `zxjt` not like '%受限%') +
        (NULLIF(`zjgs`, '') IS NOT NULL AND `zjgs` not like '%受限%') +
        (NULLIF(`haitongzq`, '') IS NOT NULL AND `haitongzq` not like '%受限%') +
        (NULLIF(`swhy`, '') IS NOT NULL AND `swhy` not like '%受限%') +
        (NULLIF(`gfzq`, '') IS NOT NULL AND `gfzq` not like '%受限%') +
        (NULLIF(`zszq`, '') IS NOT NULL AND `zszq` not like '%受限%') +
        (NULLIF(`gxzq`, '') IS NOT NULL AND `gxzq` not like '%受限%') +
        (NULLIF(`dfzq`, '') IS NOT NULL AND `dfzq` not like '%受限%') +
        (NULLIF(`zheshangzq`, '') IS NOT NULL AND `zheshangzq` not like '%受限%') +
        (NULLIF(`gdzq`, '') IS NOT NULL AND `gdzq` not like '%受限%') +
        (NULLIF(`dczq`, '') IS NOT NULL AND `dczq` not like '%受限%') +
        (NULLIF(`tfzq`, '') IS NOT NULL AND `tfzq` not like '%受限%') +
        (NULLIF(`xyzq`, '') IS NOT NULL AND `xyzq` not like '%受限%') +
        (NULLIF(`ztzq`, '') IS NOT NULL AND `ztzq` not like '%受限%') +
        (NULLIF(`xbzq`, '') IS NOT NULL AND `xbzq` not like '%受限%') +
        (NULLIF(`cjzq`, '') IS NOT NULL AND `cjzq` not like '%受限%') +
        (NULLIF(`gjzq`, '') IS NOT NULL AND `gjzq` not like '%受限%') +
        (NULLIF(`dbzq`, '') IS NOT NULL AND `dbzq` not like '%受限%') +
        (NULLIF(`gyzq`, '') IS NOT NULL AND `gyzq` not like '%受限%') +
        (NULLIF(`ctzq`, '') IS NOT NULL AND `ctzq` not like '%受限%') +
        (NULLIF(`dxzq`, '') IS NOT NULL AND `dxzq` not like '%受限%') +
        (NULLIF(`cczq`, '') IS NOT NULL AND `cczq` not like '%受限%') +
        (NULLIF(`hxzq`, '') IS NOT NULL AND `hxzq` not like '%受限%') +
        (NULLIF(`zyzq`, '') IS NOT NULL AND `zyzq` not like '%受限%') +
        (NULLIF(`njzq`, '') IS NOT NULL AND `njzq` not like '%受限%') +
        (NULLIF(`xnzq`, '') IS NOT NULL AND `xnzq` not like '%受限%') +
        (NULLIF(`pazq`, '') IS NOT NULL AND `pazq` not like '%受限%') +
        (NULLIF(`axzq`, '') IS NOT NULL AND `axzq` not like '%受限%')
        ), 2) AS avg_value,
        SUM(
        (NULLIF(`zxzq`, '') IS NOT NULL AND `zxzq` NOT LIKE '%受限%') +
        (NULLIF(`zgyh`, '') IS NOT NULL AND `zgyh` NOT LIKE '%受限%') +
        (NULLIF(`gtja`, '') IS NOT NULL AND `gtja` NOT LIKE '%受限%') +
        (NULLIF(`htzq`, '') IS NOT NULL AND `htzq` NOT LIKE '%受限%') +
        (NULLIF(`zxjt`, '') IS NOT NULL AND `zxjt` NOT LIKE '%受限%') +
        (NULLIF(`zjgs`, '') IS NOT NULL AND `zjgs` NOT LIKE '%受限%') +
        (NULLIF(`haitongzq`, '') IS NOT NULL AND `haitongzq` NOT LIKE '%受限%') +
        (NULLIF(`swhy`, '') IS NOT NULL AND `swhy` NOT LIKE '%受限%') +
        (NULLIF(`gfzq`, '') IS NOT NULL AND `gfzq` NOT LIKE '%受限%') +
        (NULLIF(`zszq`, '') IS NOT NULL AND `zszq` NOT LIKE '%受限%') +
        (NULLIF(`gxzq`, '') IS NOT NULL AND `gxzq` NOT LIKE '%受限%') +
        (NULLIF(`dfzq`, '') IS NOT NULL AND `dfzq` NOT LIKE '%受限%') +
        (NULLIF(`zheshangzq`, '') IS NOT NULL AND `zheshangzq` NOT LIKE '%受限%') +
        (NULLIF(`gdzq`, '') IS NOT NULL AND `gdzq` NOT LIKE '%受限%') +
        (NULLIF(`dczq`, '') IS NOT NULL AND `dczq` NOT LIKE '%受限%') +
        (NULLIF(`tfzq`, '') IS NOT NULL AND `tfzq` NOT LIKE '%受限%') +
        (NULLIF(`xyzq`, '') IS NOT NULL AND `xyzq` NOT LIKE '%受限%') +
        (NULLIF(`ztzq`, '') IS NOT NULL AND `ztzq` NOT LIKE '%受限%') +
        (NULLIF(`xbzq`, '') IS NOT NULL AND `xbzq` NOT LIKE '%受限%') +
        (NULLIF(`cjzq`, '') IS NOT NULL AND `cjzq` NOT LIKE '%受限%') +
        (NULLIF(`gjzq`, '') IS NOT NULL AND `gjzq` NOT LIKE '%受限%') +
        (NULLIF(`dbzq`, '') IS NOT NULL AND `dbzq` NOT LIKE '%受限%') +
        (NULLIF(`gyzq`, '') IS NOT NULL AND `gyzq` NOT LIKE '%受限%') +
        (NULLIF(`ctzq`, '') IS NOT NULL AND `ctzq` NOT LIKE '%受限%') +
        (NULLIF(`dxzq`, '') IS NOT NULL AND `dxzq` NOT LIKE '%受限%') +
        (NULLIF(`cczq`, '') IS NOT NULL AND `cczq` NOT LIKE '%受限%') +
        (NULLIF(`hxzq`, '') IS NOT NULL AND `hxzq` NOT LIKE '%受限%') +
        (NULLIF(`zyzq`, '') IS NOT NULL AND `zyzq` NOT LIKE '%受限%') +
        (NULLIF(`njzq`, '') IS NOT NULL AND `njzq` NOT LIKE '%受限%') +
        (NULLIF(`xnzq`, '') IS NOT NULL AND `xnzq` NOT LIKE '%受限%') +
        (NULLIF(`pazq`, '') IS NOT NULL AND `pazq` NOT LIKE '%受限%') +
        (NULLIF(`axzq`, '') IS NOT NULL AND `axzq` NOT LIKE '%受限%')
        ) AS include_peer_count
        FROM margin.t_peer_sec_collateral_data
        WHERE date = #{tradingDate,jdbcType=VARCHAR}
        GROUP BY `sec_code`) temp
        ON t.`sec_code` = temp.`sec_code` AND t.`date` = temp.`date`
        SET t.`avg_value` = temp.`avg_value`,
        t.include_peer_count = temp.include_peer_count;
    </update>

    <update id="updatePeerAverageFinanceTargetData">
        UPDATE margin.`t_peer_sec_finance_target_data` t
            JOIN (SELECT `sec_code`,
                         `date`,
                         ROUND(SUM(
                                       COALESCE(NULLIF(`zxzq`, ''),
                                                IF(`zxzq` = '未公开', null, `zxzq`), 0) +
                                       COALESCE(NULLIF(`zgyh`, ''),
                                                IF(`zgyh` = '未公开', null, `zgyh`), 0) +
                                       COALESCE(NULLIF(`gtja`, ''),
                                                IF(`gtja` = '未公开', null, `gtja`), 0) +
                                       COALESCE(NULLIF(`htzq`, ''),
                                                IF(`htzq` = '未公开', null, `htzq`), 0) +
                                       COALESCE(NULLIF(`zxjt`, ''),
                                                IF(`zxjt` = '未公开', null, `zxjt`), 0) +
                                       COALESCE(NULLIF(`zjgs`, ''),
                                                IF(`zjgs` = '未公开', null, `zjgs`), 0) +
                                       COALESCE(NULLIF(`haitongzq`, ''),
                                                IF(`haitongzq` = '未公开', null, `haitongzq`), 0) +
                                       COALESCE(NULLIF(`swhy`, ''),
                                                IF(`swhy` = '未公开', null, `swhy`), 0) +
                                       COALESCE(NULLIF(`gfzq`, ''),
                                                IF(`gfzq` = '未公开', null, `gfzq`), 0) +
                                       COALESCE(NULLIF(`zszq`, ''),
                                                IF(`zszq` = '未公开', null, `zszq`), 0) +
                                       COALESCE(NULLIF(`gxzq`, ''),
                                                IF(`gxzq` = '未公开', null, `gxzq`), 0) +
                                       COALESCE(NULLIF(`dfzq`, ''),
                                                IF(`dfzq` = '未公开', null, `dfzq`), 0) +
                                       COALESCE(NULLIF(`zheshangzq`, ''),
                                                IF(`zheshangzq` = '未公开', null, `zheshangzq`), 0) +
                                       COALESCE(NULLIF(`gdzq`, ''),
                                                IF(`gdzq` = '未公开', null, `gdzq`), 0) +
                                       COALESCE(NULLIF(`dczq`, ''),
                                                IF(`dczq` = '未公开', null, `dczq`), 0) +
                                       COALESCE(NULLIF(`tfzq`, ''),
                                                IF(`tfzq` = '未公开', null, `tfzq`), 0) +
                                       COALESCE(NULLIF(`xyzq`, ''),
                                                IF(`xyzq` = '未公开', null, `xyzq`), 0) +
                                       COALESCE(NULLIF(`ztzq`, ''),
                                                IF(`ztzq` = '未公开', null, `ztzq`), 0) +
                                       COALESCE(NULLIF(`xbzq`, ''),
                                                IF(`xbzq` = '未公开', null, `xbzq`), 0) +
                                       COALESCE(NULLIF(`cjzq`, ''),
                                                IF(`cjzq` = '未公开', null, `cjzq`), 0) +
                                       COALESCE(NULLIF(`gjzq`, ''),
                                                IF(`gjzq` = '未公开', null, `gjzq`), 0) +
                                       COALESCE(NULLIF(`dbzq`, ''),
                                                IF(`dbzq` = '未公开', null, `dbzq`), 0) +
                                       COALESCE(NULLIF(`gyzq`, ''),
                                                IF(`gyzq` = '未公开', null, `gyzq`), 0) +
                                       COALESCE(NULLIF(`ctzq`, ''),
                                                IF(`ctzq` = '未公开', null, `ctzq`), 0) +
                                       COALESCE(NULLIF(`dxzq`, ''),
                                                IF(`dxzq` = '未公开', null, `dxzq`), 0) +
                                       COALESCE(NULLIF(`cczq`, ''),
                                                IF(`cczq` = '未公开', null, `cczq`), 0) +
                                       COALESCE(NULLIF(`hxzq`, ''),
                                                IF(`hxzq` = '未公开', null, `hxzq`), 0) +
                                       COALESCE(NULLIF(`zyzq`, ''),
                                                IF(`zyzq` = '未公开', null, `zyzq`), 0) +
                                       COALESCE(NULLIF(`njzq`, ''),
                                                IF(`njzq` = '未公开', null, `njzq`), 0) +
                                       COALESCE(NULLIF(`xnzq`, ''),
                                                IF(`xnzq` = '未公开', null, `xnzq`), 0) +
                                       COALESCE(NULLIF(`pazq`, ''),
                                                IF(`pazq` = '未公开', null, `pazq`), 0) +
                                        COALESCE(NULLIF(`axzq`, ''),
                                        IF(`axzq` = '未公开', null, `axzq`), 0)
                               ) /
                               SUM(
                                       (NULLIF(`zxzq`, '') IS NOT NULL and `zxzq` != '未公开') +
                                       (NULLIF(`zgyh`, '') IS NOT NULL and `zgyh` != '未公开') +
                                       (NULLIF(`gtja`, '') IS NOT NULL and `gtja` != '未公开') +
                                       (NULLIF(`htzq`, '') IS NOT NULL and `htzq` != '未公开') +
                                       (NULLIF(`zxjt`, '') IS NOT NULL and `zxjt` != '未公开') +
                                       (NULLIF(`zjgs`, '') IS NOT NULL and `zjgs` != '未公开') +
                                       (NULLIF(`haitongzq`, '') IS NOT NULL and `haitongzq` != '未公开') +
                                       (NULLIF(`swhy`, '') IS NOT NULL and `swhy` != '未公开') +
                                       (NULLIF(`gfzq`, '') IS NOT NULL and `gfzq` != '未公开') +
                                       (NULLIF(`zszq`, '') IS NOT NULL and `zszq` != '未公开') +
                                       (NULLIF(`gxzq`, '') IS NOT NULL and `gxzq` != '未公开') +
                                       (NULLIF(`dfzq`, '') IS NOT NULL and `dfzq` != '未公开') +
                                       (NULLIF(`zheshangzq`, '') IS NOT NULL and `zheshangzq` != '未公开') +
                                       (NULLIF(`gdzq`, '') IS NOT NULL and `gdzq` != '未公开') +
                                       (NULLIF(`dczq`, '') IS NOT NULL and `dczq` != '未公开') +
                                       (NULLIF(`tfzq`, '') IS NOT NULL and `tfzq` != '未公开') +
                                       (NULLIF(`xyzq`, '') IS NOT NULL and `xyzq` != '未公开') +
                                       (NULLIF(`ztzq`, '') IS NOT NULL and `ztzq` != '未公开') +
                                       (NULLIF(`xbzq`, '') IS NOT NULL and `xbzq` != '未公开') +
                                       (NULLIF(`cjzq`, '') IS NOT NULL and `cjzq` != '未公开') +
                                       (NULLIF(`gjzq`, '') IS NOT NULL and `gjzq` != '未公开') +
                                       (NULLIF(`dbzq`, '') IS NOT NULL and `dbzq` != '未公开') +
                                       (NULLIF(`gyzq`, '') IS NOT NULL and `gyzq` != '未公开') +
                                       (NULLIF(`ctzq`, '') IS NOT NULL and `ctzq` != '未公开') +
                                       (NULLIF(`dxzq`, '') IS NOT NULL and `dxzq` != '未公开') +
                                       (NULLIF(`cczq`, '') IS NOT NULL and `cczq` != '未公开') +
                                       (NULLIF(`hxzq`, '') IS NOT NULL and `hxzq` != '未公开') +
                                       (NULLIF(`zyzq`, '') IS NOT NULL and `zyzq` != '未公开') +
                                       (NULLIF(`njzq`, '') IS NOT NULL and `njzq` != '未公开') +
                                       (NULLIF(`xnzq`, '') IS NOT NULL and `xnzq` != '未公开') +
                                       (NULLIF(`pazq`, '') IS NOT NULL and `pazq` != '未公开') +
                                       (NULLIF(`axzq`, '') IS NOT NULL and `axzq` != '未公开')
                               ), 2) AS avg_value,
                         SUM(
                                 (NULLIF(`zxzq`, '') IS NOT NULL) +
                                 (NULLIF(`zgyh`, '') IS NOT NULL) +
                                 (NULLIF(`gtja`, '') IS NOT NULL) +
                                 (NULLIF(`htzq`, '') IS NOT NULL) +
                                 (NULLIF(`zxjt`, '') IS NOT NULL) +
                                 (NULLIF(`zjgs`, '') IS NOT NULL) +
                                 (NULLIF(`haitongzq`, '') IS NOT NULL) +
                                 (NULLIF(`swhy`, '') IS NOT NULL) +
                                 (NULLIF(`gfzq`, '') IS NOT NULL) +
                                 (NULLIF(`zszq`, '') IS NOT NULL) +
                                 (NULLIF(`gxzq`, '') IS NOT NULL) +
                                 (NULLIF(`dfzq`, '') IS NOT NULL) +
                                 (NULLIF(`zheshangzq`, '') IS NOT NULL) +
                                 (NULLIF(`gdzq`, '') IS NOT NULL) +
                                 (NULLIF(`dczq`, '') IS NOT NULL) +
                                 (NULLIF(`tfzq`, '') IS NOT NULL) +
                                 (NULLIF(`xyzq`, '') IS NOT NULL) +
                                 (NULLIF(`ztzq`, '') IS NOT NULL) +
                                 (NULLIF(`xbzq`, '') IS NOT NULL) +
                                 (NULLIF(`cjzq`, '') IS NOT NULL) +
                                 (NULLIF(`gjzq`, '') IS NOT NULL) +
                                 (NULLIF(`dbzq`, '') IS NOT NULL) +
                                 (NULLIF(`gyzq`, '') IS NOT NULL) +
                                 (NULLIF(`ctzq`, '') IS NOT NULL) +
                                 (NULLIF(`dxzq`, '') IS NOT NULL) +
                                 (NULLIF(`cczq`, '') IS NOT NULL) +
                                 (NULLIF(`hxzq`, '') IS NOT NULL) +
                                 (NULLIF(`zyzq`, '') IS NOT NULL) +
                                 (NULLIF(`njzq`, '') IS NOT NULL) +
                                 (NULLIF(`xnzq`, '') IS NOT NULL) +
                                 (NULLIF(`pazq`, '') IS NOT NULL) +
                                 (NULLIF(`axzq`, '') IS NOT NULL)
                         )           as include_peer_count
                  FROM margin.`t_peer_sec_finance_target_data`
                  where date = #{tradingDate,jdbcType=VARCHAR}
                  GROUP BY `sec_code`) temp
            ON t.`sec_code` = temp.`sec_code` AND t.`date` = temp.`date`
        SET t.`avg_value`        = temp.`avg_value`,
            t.include_peer_count = temp.include_peer_count;
    </update>

    <update id="updatePeerAverageShortSellTargetData">
        UPDATE margin.t_peer_sec_short_sell_target_data t
        JOIN (SELECT `sec_code`,
        `date`,
        ROUND(SUM(
        COALESCE(NULLIF(`zxzq`, ''),
        IF(`zxzq` = '未公开', null, `zxzq`), 0) +
        COALESCE(NULLIF(`zgyh`, ''),
        IF(`zgyh` = '未公开', null, `zgyh`), 0) +
        COALESCE(NULLIF(`gtja`, ''),
        IF(`gtja` = '未公开', null, `gtja`), 0) +
        COALESCE(NULLIF(`htzq`, ''),
        IF(`htzq` = '未公开', null, `htzq`), 0) +
        COALESCE(NULLIF(`zxjt`, ''),
        IF(`zxjt` = '未公开', null, `zxjt`), 0) +
        COALESCE(NULLIF(`zjgs`, ''),
        IF(`zjgs` = '未公开', null, `zjgs`), 0) +
        COALESCE(NULLIF(`haitongzq`, ''),
        IF(`haitongzq` = '未公开', null, `haitongzq`), 0) +
        COALESCE(NULLIF(`swhy`, ''),
        IF(`swhy` = '未公开', null, `swhy`), 0) +
        COALESCE(NULLIF(`gfzq`, ''),
        IF(`gfzq` = '未公开', null, `gfzq`), 0) +
        COALESCE(NULLIF(`zszq`, ''),
        IF(`zszq` = '未公开', null, `zszq`), 0) +
        COALESCE(NULLIF(`gxzq`, ''),
        IF(`gxzq` = '未公开', null, `gxzq`), 0) +
        COALESCE(NULLIF(`dfzq`, ''),
        IF(`dfzq` = '未公开', null, `dfzq`), 0) +
        COALESCE(NULLIF(`zheshangzq`, ''),
        IF(`zheshangzq` = '未公开', null, `zheshangzq`), 0) +
        COALESCE(NULLIF(`gdzq`, ''),
        IF(`gdzq` = '未公开', null, `gdzq`), 0) +
        COALESCE(NULLIF(`dczq`, ''),
        IF(`dczq` = '未公开', null, `dczq`), 0) +
        COALESCE(NULLIF(`tfzq`, ''),
        IF(`tfzq` = '未公开', null, `tfzq`), 0) +
        COALESCE(NULLIF(`xyzq`, ''),
        IF(`xyzq` = '未公开', null, `xyzq`), 0) +
        COALESCE(NULLIF(`ztzq`, ''),
        IF(`ztzq` = '未公开', null, `ztzq`), 0) +
        COALESCE(NULLIF(`xbzq`, ''),
        IF(`xbzq` = '未公开', null, `xbzq`), 0) +
        COALESCE(NULLIF(`cjzq`, ''),
        IF(`cjzq` = '未公开', null, `cjzq`), 0) +
        COALESCE(NULLIF(`gjzq`, ''),
        IF(`gjzq` = '未公开', null, `gjzq`), 0) +
        COALESCE(NULLIF(`dbzq`, ''),
        IF(`dbzq` = '未公开', null, `dbzq`), 0) +
        COALESCE(NULLIF(`gyzq`, ''),
        IF(`gyzq` = '未公开', null, `gyzq`), 0) +
        COALESCE(NULLIF(`ctzq`, ''),
        IF(`ctzq` = '未公开', null, `ctzq`), 0) +
        COALESCE(NULLIF(`dxzq`, ''),
        IF(`dxzq` = '未公开', null, `dxzq`), 0) +
        COALESCE(NULLIF(`cczq`, ''),
        IF(`cczq` = '未公开', null, `cczq`), 0) +
        COALESCE(NULLIF(`hxzq`, ''),
        IF(`hxzq` = '未公开', null, `hxzq`), 0) +
        COALESCE(NULLIF(`zyzq`, ''),
        IF(`zyzq` = '未公开', null, `zyzq`), 0) +
        COALESCE(NULLIF(`njzq`, ''),
        IF(`njzq` = '未公开', null, `njzq`), 0) +
        COALESCE(NULLIF(`xnzq`, ''),
        IF(`xnzq` = '未公开', null, `xnzq`), 0) +
        COALESCE(NULLIF(`pazq`, ''),
        IF(`pazq` = '未公开', null, `pazq`), 0) +
        COALESCE(NULLIF(`axzq`, ''),
        IF(`axzq` = '未公开', null, `axzq`), 0)
        ) /
        SUM(
        (NULLIF(`zxzq`, '') IS NOT NULL and `zxzq` != '未公开') +
        (NULLIF(`zgyh`, '') IS NOT NULL and `zgyh` != '未公开') +
        (NULLIF(`gtja`, '') IS NOT NULL and `gtja` != '未公开') +
        (NULLIF(`htzq`, '') IS NOT NULL and `htzq` != '未公开') +
        (NULLIF(`zxjt`, '') IS NOT NULL and `zxjt` != '未公开') +
        (NULLIF(`zjgs`, '') IS NOT NULL and `zjgs` != '未公开') +
        (NULLIF(`haitongzq`, '') IS NOT NULL and `haitongzq` != '未公开') +
        (NULLIF(`swhy`, '') IS NOT NULL and `swhy` != '未公开') +
        (NULLIF(`gfzq`, '') IS NOT NULL and `gfzq` != '未公开') +
        (NULLIF(`zszq`, '') IS NOT NULL and `zszq` != '未公开') +
        (NULLIF(`gxzq`, '') IS NOT NULL and `gxzq` != '未公开') +
        (NULLIF(`dfzq`, '') IS NOT NULL and `dfzq` != '未公开') +
        (NULLIF(`zheshangzq`, '') IS NOT NULL and `zheshangzq` != '未公开') +
        (NULLIF(`gdzq`, '') IS NOT NULL and `gdzq` != '未公开') +
        (NULLIF(`dczq`, '') IS NOT NULL and `dczq` != '未公开') +
        (NULLIF(`tfzq`, '') IS NOT NULL and `tfzq` != '未公开') +
        (NULLIF(`xyzq`, '') IS NOT NULL and `xyzq` != '未公开') +
        (NULLIF(`ztzq`, '') IS NOT NULL and `ztzq` != '未公开') +
        (NULLIF(`xbzq`, '') IS NOT NULL and `xbzq` != '未公开') +
        (NULLIF(`cjzq`, '') IS NOT NULL and `cjzq` != '未公开') +
        (NULLIF(`gjzq`, '') IS NOT NULL and `gjzq` != '未公开') +
        (NULLIF(`dbzq`, '') IS NOT NULL and `dbzq` != '未公开') +
        (NULLIF(`gyzq`, '') IS NOT NULL and `gyzq` != '未公开') +
        (NULLIF(`ctzq`, '') IS NOT NULL and `ctzq` != '未公开') +
        (NULLIF(`dxzq`, '') IS NOT NULL and `dxzq` != '未公开') +
        (NULLIF(`cczq`, '') IS NOT NULL and `cczq` != '未公开') +
        (NULLIF(`hxzq`, '') IS NOT NULL and `hxzq` != '未公开') +
        (NULLIF(`zyzq`, '') IS NOT NULL and `zyzq` != '未公开') +
        (NULLIF(`njzq`, '') IS NOT NULL and `njzq` != '未公开') +
        (NULLIF(`xnzq`, '') IS NOT NULL and `xnzq` != '未公开') +
        (NULLIF(`pazq`, '') IS NOT NULL and `pazq` != '未公开') +
        (NULLIF(`axzq`, '') IS NOT NULL and `axzq` != '未公开')
        ), 2) AS avg_value,
        SUM(
        (NULLIF(`zxzq`, '') IS NOT NULL) +
        (NULLIF(`zgyh`, '') IS NOT NULL) +
        (NULLIF(`gtja`, '') IS NOT NULL) +
        (NULLIF(`htzq`, '') IS NOT NULL) +
        (NULLIF(`zxjt`, '') IS NOT NULL) +
        (NULLIF(`zjgs`, '') IS NOT NULL) +
        (NULLIF(`haitongzq`, '') IS NOT NULL) +
        (NULLIF(`swhy`, '') IS NOT NULL) +
        (NULLIF(`gfzq`, '') IS NOT NULL) +
        (NULLIF(`zszq`, '') IS NOT NULL) +
        (NULLIF(`gxzq`, '') IS NOT NULL) +
        (NULLIF(`dfzq`, '') IS NOT NULL) +
        (NULLIF(`zheshangzq`, '') IS NOT NULL) +
        (NULLIF(`gdzq`, '') IS NOT NULL) +
        (NULLIF(`dczq`, '') IS NOT NULL) +
        (NULLIF(`tfzq`, '') IS NOT NULL) +
        (NULLIF(`xyzq`, '') IS NOT NULL) +
        (NULLIF(`ztzq`, '') IS NOT NULL) +
        (NULLIF(`xbzq`, '') IS NOT NULL) +
        (NULLIF(`cjzq`, '') IS NOT NULL) +
        (NULLIF(`gjzq`, '') IS NOT NULL) +
        (NULLIF(`dbzq`, '') IS NOT NULL) +
        (NULLIF(`gyzq`, '') IS NOT NULL) +
        (NULLIF(`ctzq`, '') IS NOT NULL) +
        (NULLIF(`dxzq`, '') IS NOT NULL) +
        (NULLIF(`cczq`, '') IS NOT NULL) +
        (NULLIF(`hxzq`, '') IS NOT NULL) +
        (NULLIF(`zyzq`, '') IS NOT NULL) +
        (NULLIF(`njzq`, '') IS NOT NULL) +
        (NULLIF(`xnzq`, '') IS NOT NULL) +
        (NULLIF(`pazq`, '') IS NOT NULL) +
        (NULLIF(`axzq`, '') IS NOT NULL)
        )           as include_peer_count
        FROM margin.t_peer_sec_short_sell_target_data
        where date = #{tradingDate,jdbcType=VARCHAR}
        GROUP BY `sec_code`) temp ON t.`sec_code` = temp.`sec_code` AND t.`date` = temp.`date`
        SET t.`avg_value`        = temp.`avg_value`,
        t.include_peer_count = temp.include_peer_count;
    </update>

    <update id="updateTableMarketField">

        update credit.t_peer_mrg_trd_market_temp f,
            credit.${tableName} as z
        set z.market = f.market,z.if_market_checked = 0
        WHERE f.table_name = #{tableName,jdbcType=VARCHAR} and f.table_data_id = z.id;

    </update>

    <delete id="truncatePeerMrgTrdMarketTempTable">
        truncate credit.t_peer_mrg_trd_market_temp;
    </delete>

    <update id="updateDuplicateRecordIfMarketCheckedToOne">
        update
            credit.${tableName} as z,
            (SELECT z.sse_date,
                    z.sec_code
             FROM credit.${tableName} as z
             WHERE z.if_market_checked = 0
               AND z.sse_date > DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
             GROUP BY z.sse_date, z.sec_code, z.market
             HAVING COUNT(*) > 1) as error_record
        set z.if_market_checked = 1
        WHERE z.sec_code = error_record.sec_code
          and z.sse_date = error_record.sse_date;
    </update>

    <select id="selectDuplicateStockRecordId" resultType="java.lang.Integer">
        SELECT id
        FROM (SELECT MIN(z.id) AS id
              FROM credit.${tableName} AS z
                       INNER JOIN credit.t_rzrq_sec_info AS si
                                  ON z.sec_code = si.`code` AND si.sec_type = 0
              WHERE z.sse_date >= DATE_SUB(CURRENT_DATE, INTERVAL 2 DAY)
              GROUP BY z.sse_date, z.sec_code
              HAVING COUNT(0) > 1) AS keep_ids
    </select>

    <delete id="deleteDuplicateStockRecord">
        DELETE FROM credit.t_rzrq_xnzq_zsl
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
