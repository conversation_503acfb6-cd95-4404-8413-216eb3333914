# 🚀 modify_database_data_service 项目概览

> 一个专业的金融数据处理微服务，就像一个勤劳的数据管家，24/7不停地为你整理和更新各种金融数据！💼✨

## 📋 项目简介

`modify_database_data_service` 是一个基于 Spring Boot 的金融数据处理微服务，专门负责股票、市场行情、同业券商、融资融券等金融数据的自动化处理和更新。这个项目就像一个超级数据工厂，通过定时任务不断地从各个数据源获取、清洗、转换和存储金融数据！🏭📊

## 🎯 核心功能

### 📈 股票数据处理
- **股东信息管理**：自动更新上市公司十大股东信息
- **股份冻结监控**：实时跟踪股东股份冻结情况
- **市场行情分析**：计算股票质押比例、担保物比例等关键指标
- **违规信息处理**：去重和整理上市公司违规记录

### 📊 市场行情服务
- **交易日管理**：维护证券交易日历数据
- **静态市盈率监控**：跟踪交易所静态市盈率规则变化
- **历史数据归档**：自动备份和管理历史行情数据

### 🤝 同业数据分析
- **券商数据对比**：收集和分析各大券商的业务数据
- **市场字段修复**：智能修复同业数据中的市场标识错误
- **证券分类统计**：按不同维度统计同业证券数据

### 💰 融资融券业务
- **外规阈值监控**：跟踪交易所外规业务参数阈值变化
- **证券具体值计算**：计算各证券对应的具体业务参数值

## 🏗️ 技术架构

### 🔧 技术栈
- **框架版本**：Spring Boot 2.7.18 + Java 17
- **数据访问**：MyBatis-Plus 3.5.5 + 多数据源支持
- **数据库**：MySQL 8.0.33 + Druid 连接池
- **缓存方案**：Caffeine 本地缓存
- **日志系统**：Logback + 自定义监控日志
- **工具库**：Hutool 5.8.26 + Lombok 1.18.34

### 🏛️ 模块结构
```
modify_database_data_service/
├── 📁 common-pom/          # 公共依赖管理
├── 📁 modify-service/      # 核心业务模块
│   ├── 📁 stock/          # 股票数据处理
│   ├── 📁 market/         # 市场行情服务
│   ├── 📁 peer/           # 同业数据分析
│   ├── 📁 margin/         # 融资融券业务
│   └── 📁 common/         # 公共组件
├── 📁 config/             # 环境配置文件
└── 📁 docs/              # 项目文档
```

## ⚡ 核心特性

### 🕐 智能定时任务
- **高频更新**：每10分钟自动执行数据更新任务
- **分布式锁**：确保多实例环境下任务不重复执行
- **环境隔离**：只在生产环境执行关键任务

### 🚀 性能优化
- **并行处理**：使用 `ParallelExecutorUtil` 提升数据处理效率
- **异步执行**：关键业务逻辑支持异步处理
- **本地缓存**：Caffeine 缓存减少数据库访问

### 🔒 数据安全
- **分布式锁**：Redis 分布式锁保证数据一致性
- **事务管理**：完善的事务控制机制
- **错误恢复**：智能的数据修复和容错机制

### 📝 监控日志
- **分级日志**：ERROR、WARN、INFO 分别记录到不同文件
- **代码监控**：专门的代码执行监控日志
- **性能追踪**：详细的方法执行时间记录

## 🗄️ 数据库设计

### 主要数据库
- **tj_middle_ground**：中台核心数据库，存储股票基础信息
- **pledgedata**：质押相关数据，包括股东信息、市场历史等
- **margin**：融资融券业务数据
- **credit**：信用业务相关数据
- **labels**：标签和分类数据

### 核心数据表
- `t_stock_info`：股票基础信息表
- `t_latest_holder_info`：最新十大股东表
- `t_holder_share_frozen`：股东股份冻结表
- `t_sec_trading_day`：证券交易日表
- `t_exchange_static_pe_sec`：交易所静态市盈率证券表

## 🌟 项目亮点

### 💡 智能数据修复
项目具备强大的数据自愈能力，能够：
- 自动识别和修复同业数据中的市场字段错误
- 智能匹配证券名称，补全缺失的市场信息
- 基于历史数据进行数据一致性校验

### 🔄 高效数据处理
- **批量处理**：支持大批量数据的高效处理
- **增量更新**：只处理变化的数据，避免全量更新
- **并行计算**：多线程并行处理提升性能

### 📊 完善的监控体系
- **实时监控**：关键业务指标实时监控
- **异常告警**：自动识别和记录异常情况
- **性能分析**：详细的性能数据分析

## 🚀 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- MySQL 8.0+
- Redis（用于分布式锁）

### 启动步骤
1. **配置数据库**：修改 `config/application-prod.yml` 中的数据库连接信息
2. **启动应用**：运行 `ModifyApplication.main()` 方法
3. **验证服务**：访问 `http://localhost:8089` 确认服务正常启动

## 📈 性能指标

- **数据处理能力**：每分钟可处理数万条金融数据记录
- **响应时间**：平均API响应时间 < 100ms
- **可用性**：99.9% 服务可用性保证
- **并发支持**：支持高并发数据处理请求

---

> 💡 **提示**：这个项目就像一个精密的瑞士手表，每个组件都经过精心设计，确保金融数据处理的准确性和及时性！⌚✨

下一步：查看 [架构设计文档](./02-架构设计.md) 了解更多技术细节！
