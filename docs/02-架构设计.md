# 🏗️ 架构设计文档

> 深入探索 modify_database_data_service 的技术架构，就像拆解一台精密的机器，每个齿轮都有它的使命！⚙️🔧

## 🎯 架构概述

本项目采用经典的分层架构设计，结合微服务理念，构建了一个高性能、高可用的金融数据处理系统。整个架构就像一座精心设计的摩天大楼，每一层都有明确的职责和边界！🏢✨

## 🏛️ 分层架构详解

### 🌐 应用层 (Controller Layer)
**职责**：接收HTTP请求，参数验证，响应格式化

#### 核心控制器
- **StockController** 📈
  - 股东数据自动更新：`/autoUpdateCompanyShareholderData`
  - 重大事项数据更新：`/autoUpdateCompanyMaterialEventData`
  - 市场行情数据更新：`/autoUpdateStockMarketData`

- **MarketController** 📊
  - 市场行情数据更新：`/autoUpdateMarketData`
  - 交易日数据更新：`/autoUpdateStockTradingDayData`

- **PeerDataController** 🤝
  - 同业数据自动更新：`/autoUpdatePeerData`

- **MrgTrdBusinessController** 💰
  - 外规阈值数据更新：`/autoUpdateExchangeRuleThresholdSecData`

#### 设计特点
```java
@RestController
@RequestMapping("/stock")
@RequiredArgsConstructor
@Slf4j
@Validated
public class StockController {
    
    @Operation(summary = "自动更新上市公司股东相关数据")
    @GetMapping("/autoUpdateCompanyShareholderData")
    @PassToken  // 🔓 跳过token验证
    @Scheduled(fixedDelay = 1000 * 60 * 10)  // ⏰ 每10分钟执行
    @ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})  // 🎯 仅生产环境
    @SingleInstanceLock  // 🔒 分布式锁
    public void autoUpdateCompanyShareholderData() {
        // 业务逻辑
    }
}
```

### ⚙️ 服务层 (Service Layer)
**职责**：核心业务逻辑处理，事务管理，数据转换

#### 🔥 核心服务详解

##### 📈 StockUpdateUtilService
```java
public interface StockUpdateUtilService {
    // 去重股东减持公告表
    void distinctHolderReductionTable();
    
    // 去重违规信息表
    void distinctCriminalRecordTable();
    
    // 更新股东股份冻结表
    void autoUpdateHolderShareFrozenTable();
    
    // 更新全市场单一股票质押比例
    void updateStockMarketPledgeRatio();
    
    // 更新全市场单一股票担保物比例
    void updateStockMarketCollateralRatio();
}
```

**设计亮点**：
- 🔒 **分布式锁保护**：关键更新操作使用Redis分布式锁
- ⚡ **性能优化**：批量处理减少数据库交互
- 🛡️ **异常处理**：完善的异常捕获和恢复机制

##### 🤝 PeerDataService
**核心功能**：同业券商数据处理的"大脑"

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class PeerDataService {
    
    // 🔧 修复同业表数据
    public void fixPeerTableData() {
        // 添加字段
        updateRealUtil.executeUpdateMethodOneParam(
            this::addPeerTableDesignateField, 
            PeerConsts.TABLE_FIELD_ENABLE_STATUS
        );
        
        // 修复市场字段
        updateRealUtil.executeUpdateMethod(
            this::fixPeerTableMarketFieldNullValue, 
            "fixPeerTableMarketFieldNullValue"
        );
    }
    
    // 🚀 并行更新同业证券数据
    private void updateDateListPeerSecData(List<String> tradingDateList) {
        List<CompletableFuture<Void>> tasks = new ArrayList<>();
        
        // 创建并行任务
        for (String tradingDate : tradingDateList) {
            tasks.add(CompletableFuture.runAsync(() -> 
                updateSingleDatePeerSecData(tradingDate)
            ));
        }
        
        // 等待所有任务完成
        ParallelExecutorUtil.executeTasks(tasks, null, true);
    }
}
```

**智能特性**：
- 🧠 **智能数据修复**：自动识别和修复数据错误
- 🔄 **并行处理**：多线程并行提升处理效率
- 📊 **数据一致性**：确保同业数据的准确性

### 🗄️ 数据访问层 (Mapper Layer)
**职责**：数据库操作，SQL执行，结果映射

#### MyBatis-Plus 配置
```yaml
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  typeAliasesPackage: com.tjsj.**.modules.**.model
  global-config:
    banner: false
    db-config:
      id-type: auto
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

#### 多数据源配置
```yaml
spring:
  datasource:
    dynamic:
      primary: tj_middle_ground
      datasource:
        tj_middle_ground:
          url: **************************************
          username: xxx
          password: xxx
        pledgedata:
          url: ********************************
          username: xxx
          password: xxx
```

### 🗃️ 数据存储层 (Database Layer)

#### 数据库架构设计
```mermaid
erDiagram
    tj_middle_ground ||--o{ t_stock_info : contains
    tj_middle_ground ||--o{ t_sec_peer_info : contains
    tj_middle_ground ||--o{ t_exchange_static_pe_sec : contains
    
    pledgedata ||--o{ t_latest_holder_info : contains
    pledgedata ||--o{ t_stockhistory_0827 : contains
    pledgedata ||--o{ t_sec_trading_day : contains
    
    margin ||--o{ t_peer_sec_count_history : contains
    margin ||--o{ t_exchange_rule_threshold_sec : contains
    
    credit ||--o{ t_finance_settings : contains
```

## 🏗️ 基础设施层

### ⏰ 定时任务架构

#### 设计原则
- **高频执行**：每10分钟执行一次，确保数据实时性
- **环境隔离**：只在生产环境执行关键任务
- **分布式锁**：防止多实例重复执行

#### 实现机制
```java
@Component
@RequiredArgsConstructor
public class ScheduledTaskManager {
    
    @Scheduled(fixedDelay = 1000 * 60 * 10)
    @ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
    @SingleInstanceLock
    public void executeDataUpdateTask() {
        // 使用分布式锁确保单实例执行
        distributedLockHelper.executeWithLock(
            RedisConsts.DATA_UPDATE_LOCK,
            null,
            10L,
            this::performDataUpdate
        );
    }
}
```

### 🚀 性能优化架构

#### 缓存策略
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .recordStats());
        return cacheManager;
    }
}
```

#### 并行处理框架
```java
public class ParallelExecutorUtil {
    
    public static void executeTasks(
        List<CompletableFuture<Void>> tasks, 
        String taskName, 
        boolean waitForCompletion
    ) {
        if (waitForCompletion) {
            CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0]))
                .join();
        }
    }
}
```

### 📝 监控日志架构

#### 日志分级策略
```xml
<!-- ERROR级别日志 -->
<appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/${SERVICE_NAME}-${PROFILE}_error.log</file>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
        <level>ERROR</level>
        <onMatch>ACCEPT</onMatch>
        <onMismatch>DENY</onMismatch>
    </filter>
</appender>

<!-- 代码监控日志 -->
<appender name="CODE_MONITOR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>./logs/${SERVICE_NAME}-code-monitor.log</file>
</appender>
```

## 🔄 数据流架构

### 数据处理流程
```mermaid
sequenceDiagram
    participant Timer as ⏰ 定时器
    participant Controller as 🌐 控制器
    participant Service as ⚙️ 服务层
    participant Mapper as 🗄️ 数据访问层
    participant DB as 🗃️ 数据库
    
    Timer->>Controller: 触发定时任务
    Controller->>Service: 调用业务服务
    Service->>Service: 获取分布式锁
    Service->>Mapper: 查询原始数据
    Mapper->>DB: 执行SQL查询
    DB-->>Mapper: 返回数据
    Mapper-->>Service: 返回实体对象
    Service->>Service: 数据处理和转换
    Service->>Mapper: 批量更新数据
    Mapper->>DB: 执行批量更新
    DB-->>Mapper: 确认更新
    Mapper-->>Service: 返回更新结果
    Service-->>Controller: 返回处理结果
    Controller-->>Timer: 完成任务
```

## 🛡️ 安全架构

### 分布式锁机制
- **Redis实现**：基于Redis的分布式锁
- **超时保护**：防止死锁的超时机制
- **重入支持**：支持锁的重入操作

### 数据安全
- **事务保护**：关键操作使用数据库事务
- **异常恢复**：完善的异常处理和数据恢复机制
- **数据校验**：多层次的数据完整性校验

---

> 💡 **架构总结**：这个架构就像一个精密的交响乐团，每个组件都在指挥棒下协调工作，奏出完美的数据处理乐章！🎼✨

下一步：查看 [模块功能详解](./03-模块功能详解.md) 了解各模块的具体实现！
