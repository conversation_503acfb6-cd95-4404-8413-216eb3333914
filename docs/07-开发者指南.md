# 👨‍💻 开发者指南

> 欢迎加入这个精彩的金融数据处理项目！就像加入一个顶级的F1车队，让我们一起打造最快最稳的数据处理引擎！🏎️🔧

## 🚀 快速开始

### 📋 环境准备

#### 🛠️ 必需环境
- **Java**：JDK 17+ (推荐使用 OpenJDK 17)
- **Maven**：3.6+ (用于依赖管理和构建)
- **MySQL**：8.0+ (主要数据存储)
- **Redis**：6.0+ (分布式锁和缓存)
- **IDE**：IntelliJ IDEA 2023+ (推荐)

#### 🔧 开发工具推荐
```bash
# 安装 SDKMAN (Java版本管理)
curl -s "https://get.sdkman.io" | bash
sdk install java 17.0.2-open

# 安装 Maven
sdk install maven 3.9.0

# 验证环境
java -version
mvn -version
```

### 📥 项目克隆与构建

#### 1. 克隆项目
```bash
git clone <repository-url>
cd modify_database_data_service
```

#### 2. 配置数据库
```bash
# 复制配置文件模板
cp config/application-prod.yml.template config/application-prod.yml

# 编辑配置文件，修改数据库连接信息
vim config/application-prod.yml
```

**配置示例**：
```yaml
spring:
  datasource:
    dynamic:
      primary: tj_middle_ground
      datasource:
        tj_middle_ground:
          url: ********************************************
          username: your_username
          password: your_password
        pledgedata:
          url: **************************************
          username: your_username
          password: your_password
```

#### 3. 构建项目
```bash
# 清理并编译
mvn clean compile

# 运行测试
mvn test

# 打包
mvn package -DskipTests
```

#### 4. 启动应用
```bash
# 开发环境启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者运行jar包
java -jar modify-service/target/modify-service-1.0.jar --spring.profiles.active=dev
```

## 🏗️ 开发规范

### 📝 代码规范

#### 1. 命名规范
```java
// ✅ 好的命名
public class StockUpdateUtilService {
    private final LatestHolderService latestHolderService;
    
    public void autoUpdateLatestHolderData() {
        // 实现逻辑
    }
}

// ❌ 不好的命名
public class SUS {
    private final LHS lhs;
    
    public void update() {
        // 实现逻辑
    }
}
```

#### 2. 注释规范
```java
/**
 * 自动更新上市公司股东相关数据
 * 
 * 处理流程：
 * 1. 去重十大股东历史表 pledgedata.t_tenshareholderinfos
 * 2. 自动更新最新十大股东表 pledgedata.t_latest_holder_info
 * 3. 数据一致性校验和修复
 * 
 * <AUTHOR> Ye
 * @date 2025/02/10
 * @since 1.0.0
 */
@Operation(summary = "自动更新上市公司股东相关数据")
@GetMapping("/autoUpdateCompanyShareholderData")
public void autoUpdateCompanyShareholderData() {
    // 实现逻辑
}
```

#### 3. 异常处理规范
```java
@Service
@RequiredArgsConstructor
@Slf4j
public class DataProcessService {
    
    public void processData(String dataType, List<String> dataList) {
        try {
            // 业务逻辑处理
            doProcessData(dataType, dataList);
            
        } catch (DataValidationException e) {
            // 数据验证异常 - 记录并继续处理其他数据
            log.warn("数据验证失败: {}, 数据: {}", e.getMessage(), dataList);
            handleValidationFailure(dataType, dataList, e);
            
        } catch (DatabaseException e) {
            // 数据库异常 - 记录并抛出，触发重试
            log.error("数据库操作失败: {}", e.getMessage(), e);
            throw new ServiceException("数据处理失败", e);
            
        } catch (Exception e) {
            // 未知异常 - 记录详细信息并抛出
            log.error("数据处理发生未知异常: {}, 数据类型: {}", e.getMessage(), dataType, e);
            throw new ServiceException("系统异常", e);
        }
    }
}
```

### 🧪 测试规范

#### 1. 单元测试
```java
@SpringBootTest
@Transactional
@Rollback
class StockUpdateUtilServiceTest {
    
    @Autowired
    private StockUpdateUtilService stockUpdateUtilService;
    
    @MockBean
    private StockUpdateUtilMapper stockUpdateUtilMapper;
    
    @Test
    @DisplayName("测试股票质押比例更新功能")
    void testUpdateStockMarketPledgeRatio() {
        // Given
        when(stockUpdateUtilMapper.updateStockMarketPledgeRatio())
            .thenReturn(100);
        
        // When
        assertDoesNotThrow(() -> 
            stockUpdateUtilService.updateStockMarketPledgeRatio()
        );
        
        // Then
        verify(stockUpdateUtilMapper, times(1))
            .updateStockMarketPledgeRatio();
    }
    
    @Test
    @DisplayName("测试分布式锁异常情况")
    void testDistributedLockException() {
        // Given
        when(stockUpdateUtilMapper.updateStockMarketPledgeRatio())
            .thenThrow(new RuntimeException("数据库连接失败"));
        
        // When & Then
        assertThrows(RuntimeException.class, () -> 
            stockUpdateUtilService.updateStockMarketPledgeRatio()
        );
    }
}
```

#### 2. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.yml")
class StockControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    @DisplayName("测试股东数据更新接口")
    void testAutoUpdateCompanyShareholderData() {
        // When
        ResponseEntity<String> response = restTemplate.getForEntity(
            "/stock/autoUpdateCompanyShareholderData", 
            String.class
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

## 🔧 开发工作流

### 🌿 Git工作流

#### 1. 分支策略
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/stock-optimization     # 股票模块优化
feature/peer-data-fix         # 同业数据修复
feature/performance-improve   # 性能优化

# 修复分支
hotfix/critical-bug-fix       # 紧急bug修复
```

#### 2. 提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 示例
feat(stock): 添加股票质押比例计算功能
fix(peer): 修复同业数据市场字段错误
docs(api): 更新API文档
style(format): 代码格式化
refactor(service): 重构数据处理服务
test(unit): 添加单元测试
chore(deps): 更新依赖版本
```

#### 3. 代码审查清单
```markdown
## 代码审查清单 ✅

### 功能性
- [ ] 功能是否按需求正确实现
- [ ] 边界条件是否正确处理
- [ ] 异常情况是否妥善处理

### 代码质量
- [ ] 代码是否遵循项目规范
- [ ] 变量和方法命名是否清晰
- [ ] 是否有适当的注释

### 性能
- [ ] 是否存在性能瓶颈
- [ ] 数据库查询是否优化
- [ ] 是否正确使用缓存

### 安全性
- [ ] 是否存在SQL注入风险
- [ ] 敏感信息是否正确处理
- [ ] 权限控制是否到位

### 测试
- [ ] 是否有足够的单元测试
- [ ] 测试覆盖率是否达标
- [ ] 集成测试是否通过
```

## 🛠️ 调试技巧

### 🔍 日志调试

#### 1. 日志级别配置
```yaml
# application-dev.yml
logging:
  level:
    com.tjsj.modify: DEBUG
    org.springframework.jdbc: DEBUG
    com.baomidou.mybatisplus: DEBUG
```

#### 2. 自定义日志
```java
@Slf4j
@Service
public class DebugService {
    
    public void processWithDebugLog(String dataType, List<String> dataList) {
        log.debug("开始处理数据: 类型={}, 数量={}", dataType, dataList.size());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 业务处理
            processData(dataType, dataList);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("数据处理完成: 类型={}, 耗时={}ms", dataType, duration);
            
        } catch (Exception e) {
            log.error("数据处理失败: 类型={}, 错误={}", dataType, e.getMessage(), e);
            throw e;
        }
    }
}
```

### 🔧 性能调试

#### 1. SQL性能分析
```yaml
# 开启SQL日志
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

#### 2. JVM性能监控
```bash
# 启动时添加JVM参数
java -jar app.jar \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/tmp/heapdump.hprof
```

## 📊 监控和运维

### 📈 应用监控

#### 1. 健康检查
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            dataSource.getConnection().close();
            
            // 检查Redis连接
            // redisTemplate.opsForValue().get("health-check");
            
            return Health.up()
                .withDetail("database", "连接正常")
                .withDetail("redis", "连接正常")
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

#### 2. 自定义指标
```java
@Component
public class BusinessMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter dataProcessCounter;
    private final Timer dataProcessTimer;
    
    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.dataProcessCounter = Counter.builder("data.process.count")
            .description("数据处理次数")
            .register(meterRegistry);
        this.dataProcessTimer = Timer.builder("data.process.duration")
            .description("数据处理耗时")
            .register(meterRegistry);
    }
    
    public void recordDataProcess(String dataType, Runnable task) {
        dataProcessCounter.increment(Tags.of("type", dataType));
        dataProcessTimer.recordCallable(() -> {
            task.run();
            return null;
        });
    }
}
```

## 🚀 部署指南

### 🐳 Docker部署
```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY modify-service/target/modify-service-1.0.jar app.jar

EXPOSE 8089

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  modify-service:
    build: .
    ports:
      - "8089:8089"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xmx2g -Xms1g
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
      
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
```

### 🔧 生产环境配置
```bash
# 启动脚本 start.sh
#!/bin/bash

export JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
export SPRING_PROFILES_ACTIVE=prod

nohup java $JAVA_OPTS -jar modify-service-1.0.jar > /dev/null 2>&1 &

echo "应用启动中..."
sleep 5

# 检查应用是否启动成功
if curl -f http://localhost:8089/actuator/health; then
    echo "应用启动成功！"
else
    echo "应用启动失败！"
    exit 1
fi
```

## 🤝 贡献指南

### 📝 提交代码流程
1. **Fork项目** 🍴
2. **创建功能分支** 🌿
3. **编写代码和测试** 💻
4. **提交代码审查** 👀
5. **合并到主分支** 🔀

### 🏆 贡献者认可
- 优秀贡献者将被列入项目贡献者名单
- 重大功能贡献者可获得项目维护者权限
- 定期举办代码分享会，展示优秀实践

---

> 💡 **开发者寄语**：编程就像烹饪一道美食，需要精心选择食材（技术栈）、掌握火候（性能优化）、注重摆盘（代码规范），最终呈现出令人赞叹的作品！👨‍🍳✨

欢迎加入我们的开发团队，一起打造更强大的金融数据处理系统！🚀🎉
