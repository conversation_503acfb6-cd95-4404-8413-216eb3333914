#!/bin/bash
#
# =========================================================
#  🚀 Tarkin Risk Management · 单服务守护脚本 (可视化输出版)
#  功能：监听端口 ⇢ 未启动则自动拉起 ⇢ 结果彩色提示 & 写入日志
#  参考：modify-service-1.0.jar 监听 8089
# =========================================================

##### 0. 语言环境 #############################################################
export LANG="zh_CN.UTF-8"
export LC_ALL="zh_CN.UTF-8"

##### 1. 基础配置 #############################################################
APP_HOME="$(cd "$(dirname "$0")" && pwd)"
LOG_DIR="$APP_HOME/logs"
mkdir -p "${LOG_DIR}"

# 公共 JVM 参数
COMMON_JAVA_OPTS="-Dspring.profiles.active=prod -Dfile.encoding=UTF-8"

# 单服务参数：name|jar|port|memory|extraOpts
SERVICE="modify-service|modify-service-1.0.jar|8089|-Xmx512m|--add-exports java.base/sun.security.action=ALL-UNNAMED"

# 日志文件
START_LOG="$LOG_DIR/startup.log"
ERROR_LOG="$LOG_DIR/startError.log"

##### 2. 彩色输出工具 ##########################################################
green()  { echo -e "\033[32m$*\033[0m"; }
red()    { echo -e "\033[31m$*\033[0m"; }
cyan()   { echo -e "\033[36m$*\033[0m"; }
grey()   { echo -e "\033[90m$*\033[0m"; }

##### 3. 启动函数 #############################################################
start_service() {
  IFS="|" read NAME JAR PORT MEM_OPTS EXTRA_OPTS <<< "${SERVICE}"

  # 检查 JAR 是否存在
  if [ ! -f "${APP_HOME}/${JAR}" ]; then
    red "✘ 未找到可执行文件 ${JAR}"
    echo "$(date) JAR missing: ${JAR}" >> "${ERROR_LOG}"
    return 1
  fi

  cyan "📦 正在启动 ${NAME}（端口 ${PORT}） ..."
  nohup java ${EXTRA_OPTS} ${MEM_OPTS} ${COMMON_JAVA_OPTS} \
    -jar "${APP_HOME}/${JAR}" \
    > /dev/null 2>&1 &

  if [ $? -eq 0 ]; then
    green "✔ 启动成功（控制台输出已丢弃）"
    echo "$(date) ${NAME} started." >> "${START_LOG}"
  else
    red "✘ 启动失败，详见 ${ERROR_LOG}"
    echo "$(date) ${NAME} failed to start." >> "${ERROR_LOG}"
  fi
}

##### 4. 端口监控循环 #########################################################
monitor_loop() {
  IFS="|" read NAME _ PORT _ _ <<< "${SERVICE}"

  cyan "\n🔄 进入守护模式：每 600 秒检测一次端口 ${PORT}\n"

  while true; do
    if lsof -i:"${PORT}" >/dev/null 2>&1; then
      grey "$(date '+%F %T') ${NAME} 运行中 ..."
    else
      red "$(date '+%F %T') 检测到 ${NAME} 未运行，立即尝试启动！"
      start_service
      # 启动后等待 100 秒再继续监控，避免重复拉起
      sleep 100
    fi
    sleep 600
  done
}

##### 5. 执行 ##################################################################
monitor_loop

