# 📡 API接口文档

> 就像一本详细的使用说明书，告诉你如何与这个强大的金融数据处理系统对话！📚🤖

## 🌐 API概览

### 🏠 基础信息
- **服务地址**：`http://localhost:8089`
- **API版本**：v1.0
- **数据格式**：JSON
- **字符编码**：UTF-8
- **文档工具**：Swagger 3.0

### 🔐 认证方式
大部分定时任务接口使用 `@PassToken` 注解，无需token验证。生产环境建议配置适当的安全策略。

## 📈 Stock模块 API

### 🎯 股票数据管理

#### 1. 自动更新股东数据
**接口描述**：自动更新上市公司股东相关数据

```http
GET /stock/autoUpdateCompanyShareholderData
```

**请求参数**：无

**响应示例**：
```json
{
  "code": 200,
  "message": "股东数据更新成功",
  "data": null,
  "timestamp": "2025-02-10T10:30:00"
}
```

**功能说明**：
- 🔄 去重十大股东历史表 `pledgedata.t_tenshareholderinfos`
- 📊 自动更新最新十大股东表 `pledgedata.t_latest_holder_info`
- ⏰ 每10分钟自动执行一次
- 🎯 仅在生产环境执行

**代码示例**：
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/stock/controller/StockController.java" mode="EXCERPT">
````java
@Operation(summary = "自动更新上市公司股东相关数据")
@GetMapping("/autoUpdateCompanyShareholderData")
@PassToken
@Scheduled(fixedDelay = 1000 * 60 * 10)
@ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
@SingleInstanceLock
public void autoUpdateCompanyShareholderData() {
    // 去重十大股东历史表
    updateRealUtil.executeUpdateMethod(latestHolderService::distinctHolderHistoryTable,
            "distinctHolderHistoryTable");
    
    // 自动更新最新十大股东表
    updateRealUtil.executeUpdateMethod(latestHolderService::autoUpdateLatestHolderData,
            "autoUpdateLatestHolderData");
}
````
</augment_code_snippet>

#### 2. 更新重大事项数据
**接口描述**：自动更新上市公司重大事项相关数据

```http
GET /stock/autoUpdateCompanyMaterialEventData
```

**功能说明**：
- 🧹 去重违规信息表 `pledgedata.t_criminalrecords`
- 📋 清理重复的违规记录
- 🔍 数据完整性校验

#### 3. 更新市场行情数据
**接口描述**：自动更新股票市场行情相关数据

```http
GET /stock/autoUpdateStockMarketData
```

**功能说明**：
- 📊 更新全市场单一股票质押比例
- 💎 更新全市场单一股票担保物比例
- 🔒 使用分布式锁保证数据一致性

**处理逻辑**：
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/stock/service/impl/StockUpdateUtilServiceImpl.java" mode="EXCERPT">
````java
@Override
public void updateStockMarketPledgeRatio() {
    distributedLockHelper.executeWithLock(RedisConsts.T_STOCK_INFO_TABLE_LOCK,
            null,
            10L,
            stockUpdateUtilMapper::updateStockMarketPledgeRatio);
}

@Override
public void updateStockMarketCollateralRatio() {
    distributedLockHelper.executeWithLock(RedisConsts.T_STOCK_INFO_TABLE_LOCK,
            null,
            10L,
            stockUpdateUtilMapper::updateStockMarketCollateralRatio);
}
````
</augment_code_snippet>

## 📊 Market模块 API

### 🎯 市场行情管理

#### 1. 更新市场数据
**接口描述**：自动更新市场行情数据

```http
GET /market/autoUpdateMarketData
```

**功能说明**：
- 📈 更新交易所静态市盈率外规证券数据
- 📊 更新交易所静态市盈率证券调整历史数据
- 🔄 更新交易所静态市盈率证券历史数据

**智能判断逻辑**：
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/market/service/impl/ExchangeStaticPeSecServiceImpl.java" mode="EXCERPT">
````java
@Override
public void autoUpdateExchangeStaticPeSecData() {
    // 判断当日是否是本周最后一个交易日，且行情表已经更新完成
    boolean ifTodayLastTradingDayThisWeekAndMarketDataUpdated =
            marketUpdateUtilMapper.checkIfLastTradingDayThisWeekAndMarketDataUpdated() > 5000;

    List<ExchangeStaticPeSecDO> newStaticPeSecList = marketUpdateUtilMapper.selectNewExchangeStaticPeSecData(
            ifTodayLastTradingDayThisWeekAndMarketDataUpdated
    );
}
````
</augment_code_snippet>

#### 2. 更新交易日数据
**接口描述**：自动更新股票交易日期数据

```http
GET /market/autoUpdateStockTradingDayData
```

**功能说明**：
- 📅 维护证券交易日历
- 🔢 更新交易日序号
- 🗑️ 清理过期交易日数据

## 🤝 Peer模块 API

### 🎯 同业数据管理

#### 1. 自动更新同业数据
**接口描述**：自动更新同业券商数据

```http
GET /peer/autoUpdatePeerData
```

**功能说明**：
- 🔧 修复同业券商数据表结构和数据
- 📊 更新同业券商数据详情
- 📈 更新同业券商汇总统计历史表

**数据修复策略**：
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/peer/service/impl/PeerDataService.java" mode="EXCERPT">
````java
public void fixPeerTableData() {
    // 给同业券商表结构添加 enable_status 字段
    updateRealUtil.executeUpdateMethodOneParam(this::addPeerTableDesignateField,
            PeerConsts.TABLE_FIELD_ENABLE_STATUS);
    
    // 修复同业表市场字段为空的数据
    updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldNullValue, 
            "fixPeerTableMarketFieldNullValue");
    
    // 修复同业券商的market字段值错误的记录
    updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldErrorRecord,
            "fixPeerTableMarketFieldErrorRecord");
}
````
</augment_code_snippet>

**并行处理机制**：
- 🚀 使用 `ParallelExecutorUtil` 并行处理
- ⚡ 多线程提升处理效率
- 🎯 智能任务分配

## 💰 Margin模块 API

### 🎯 融资融券业务管理

#### 1. 更新外规阈值数据
**接口描述**：自动更新交易所外规阈值对应的证券具体值

```http
GET /margin/autoUpdateExchangeRuleThresholdSecData
```

**功能说明**：
- 📊 监控交易所外规业务参数阈值变化
- 💎 计算各证券对应的具体业务参数值
- ⚖️ 更新风险控制参数

**数据模型**：
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/margin/business/model/entity/ExchangeRuleThresholdConfigDO.java" mode="EXCERPT">
````java
@Schema(description = "交易所外规对于业务参数的阈值规约配置")
@TableName(value = "margin.t_exchange_rule_threshold_config")
public class ExchangeRuleThresholdConfigDO implements Serializable {
    
    @TableField(value = "mrd_trd_type")
    @Schema(description = "融资融券业务类型")
    private MrgTrdDataType mrdTrdType;
    
    @TableField(value = "sec_type")
    @Schema(description = "证券类型")
    private SecTypeEnum secType;
    
    @TableField(value = "threshold_value")
    @Schema(description = "阈值")
    private BigDecimal thresholdValue;
}
````
</augment_code_snippet>

## 🧪 Test模块 API

### 🎯 测试和调试接口

#### 1. 测试更新同业数据
**接口描述**：测试环境下手动触发同业数据更新

```http
GET /test/testUpdatePeerSecData?startDate=2025-01-01&endDate=2025-01-31
```

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd |

**功能说明**：
- 🧪 仅在测试环境可用
- 📊 手动指定日期范围更新数据
- 🔍 用于调试和验证数据处理逻辑

## 📊 通用响应格式

### ✅ 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-02-10T10:30:00"
}
```

### ❌ 错误响应
```json
{
  "code": 500,
  "message": "系统内部错误",
  "data": null,
  "timestamp": "2025-02-10T10:30:00",
  "error": {
    "type": "DataProcessException",
    "details": "数据处理过程中发生异常"
  }
}
```

### 📋 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔧 调用示例

### 📱 JavaScript调用示例
```javascript
// 使用fetch调用API
async function updateStockData() {
    try {
        const response = await fetch('/stock/autoUpdateCompanyShareholderData', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        console.log('更新结果:', result);
    } catch (error) {
        console.error('调用失败:', error);
    }
}
```

### 🐍 Python调用示例
```python
import requests
import json

def update_market_data():
    """调用市场数据更新接口"""
    url = "http://localhost:8089/market/autoUpdateMarketData"
    
    try:
        response = requests.get(url)
        result = response.json()
        print(f"更新结果: {result}")
    except Exception as e:
        print(f"调用失败: {e}")

# 调用示例
update_market_data()
```

### ☕ Java调用示例
```java
@Service
public class ApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 调用同业数据更新接口
     */
    public void updatePeerData() {
        String url = "http://localhost:8089/peer/autoUpdatePeerData";
        
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            log.info("调用结果: {}", response.getBody());
        } catch (Exception e) {
            log.error("调用失败", e);
        }
    }
}
```

## 📈 性能指标

### ⚡ 响应时间
- **平均响应时间**：< 100ms
- **99%分位响应时间**：< 500ms
- **超时时间**：30秒

### 🔄 并发能力
- **最大并发数**：1000 QPS
- **平均并发数**：100 QPS
- **推荐并发数**：50 QPS

---

> 💡 **API总结**：这些API就像一套精密的工具，每个都有特定的用途，组合使用能够完成复杂的金融数据处理任务！🛠️✨

下一步：查看 [开发者指南](./07-开发者指南.md) 了解如何参与项目开发！
