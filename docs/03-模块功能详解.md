# 🧩 模块功能详解

> 深入每个模块的内心世界，就像探索一个个精密的齿轮，看它们如何完美协作！⚙️💫

## 📈 Stock 模块 - 股票数据处理专家

### 🎯 模块概述
Stock模块是整个系统的核心引擎之一，专门负责处理上市公司相关的各种数据，包括股东信息、股份冻结、市场行情等。就像一个专业的股票分析师，24小时不间断地监控和分析股票市场！📊✨

### 🔥 核心功能

#### 1. 股东信息管理 👥
**功能描述**：自动维护上市公司十大股东信息

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/stock/controller/StockController.java" mode="EXCERPT">
````java
@Operation(summary = "自动更新上市公司股东相关数据")
@GetMapping("/autoUpdateCompanyShareholderData")
@PassToken
@Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟更新一次
@ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
@SingleInstanceLock
public void autoUpdateCompanyShareholderData() {
    // 去重十大股东历史表
    updateRealUtil.executeUpdateMethod(latestHolderService::distinctHolderHistoryTable,
            "distinctHolderHistoryTable");
    
    // 自动更新最新十大股东表
    updateRealUtil.executeUpdateMethod(latestHolderService::autoUpdateLatestHolderData,
            "autoUpdateLatestHolderData");
}
````
</augment_code_snippet>

**处理流程**：
1. 🔄 去重历史股东数据，清理重复记录
2. 📊 更新最新十大股东信息
3. 🔍 数据一致性校验和修复

#### 2. 股份冻结监控 🧊
**功能描述**：实时跟踪股东股份冻结情况

**核心实体**：
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/stock/model/HolderShareFrozenDO.java" mode="EXCERPT">
````java
@Data
@Accessors(chain = true)
@TableName(value = "tj_middle_ground.t_holder_share_frozen")
@Schema(description = "股东股份冻结详情表")
public class HolderShareFrozenDO implements Serializable {
    
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;
    
    @TableField(value = "frozen_person")
    @Schema(description = "冻结股东名称")
    private String frozenPerson;
    
    @TableField(value = "frozen_shares")
    @Schema(description = "冻结股份数量")
    private BigDecimal frozenShares;
}
````
</augment_code_snippet>

#### 3. 市场行情计算 📈
**功能描述**：计算全市场股票的关键指标

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/stock/service/impl/StockUpdateUtilServiceImpl.java" mode="EXCERPT">
````java
@Override
public void updateStockMarketPledgeRatio() {
    distributedLockHelper.executeWithLock(RedisConsts.T_STOCK_INFO_TABLE_LOCK,
            null,
            10L,
            stockUpdateUtilMapper::updateStockMarketPledgeRatio);
}

@Override
public void updateStockMarketCollateralRatio() {
    distributedLockHelper.executeWithLock(RedisConsts.T_STOCK_INFO_TABLE_LOCK,
            null,
            10L,
            stockUpdateUtilMapper::updateStockMarketCollateralRatio);
}
````
</augment_code_snippet>

**计算指标**：
- 🔒 **质押比例**：股票质押占流通股本的比例
- 💎 **担保物比例**：作为担保物的股票比例
- 📊 **市场风险指标**：综合风险评估指标

### 🗃️ 数据模型

#### 核心实体关系
```mermaid
erDiagram
    StockInfo ||--o{ LatestHolderInfo : "has holders"
    StockInfo ||--o{ HolderShareFrozen : "has frozen shares"
    StockInfo ||--o{ StockMarketHis : "has market history"
    
    StockInfo {
        string stock_id PK
        string sec_code
        string stock_name
        decimal market_value
        decimal pb_ratio
    }
    
    LatestHolderInfo {
        long id PK
        string sec_code FK
        string holder_name
        decimal hold_shares
        decimal hold_ratio
    }
    
    HolderShareFrozen {
        int id PK
        string sec_code FK
        string frozen_person
        decimal frozen_shares
        date frozen_date
    }
```

---

## 📊 Market 模块 - 市场行情分析师

### 🎯 模块概述
Market模块专注于市场行情数据的处理和分析，包括交易日管理、静态市盈率监控、历史数据归档等。就像一个专业的市场分析师，时刻关注着市场的每一个变化！📈🔍

### 🔥 核心功能

#### 1. 交易日管理 📅
**功能描述**：维护证券市场交易日历

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/market/controller/MarketController.java" mode="EXCERPT">
````java
@Operation(summary = "自动更新股票交易日期数据")
@GetMapping("/autoUpdateStockTradingDayData")
@PassToken
@Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟更新一次
@ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
@SingleInstanceLock
public void autoUpdateStockTradingDayData() {
    updateRealUtil.executeUpdateMethod(secTradingDayService::autoUpdateStockTradingDayData,
            "autoUpdateStockTradingDayData");
}
````
</augment_code_snippet>

**处理逻辑**：
- 🔄 自动识别新的交易日
- 📊 更新交易日序号
- 🗑️ 清理过期的交易日数据

#### 2. 静态市盈率监控 📊
**功能描述**：跟踪交易所静态市盈率规则变化

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/market/service/impl/ExchangeStaticPeSecServiceImpl.java" mode="EXCERPT">
````java
@Override
public void autoUpdateExchangeStaticPeSecData() {
    // 判断当日是否是本周最后一个交易日，且行情表已经更新完成
    boolean ifTodayLastTradingDayThisWeekAndMarketDataUpdated =
            marketUpdateUtilMapper.checkIfLastTradingDayThisWeekAndMarketDataUpdated() > 5000;

    List<ExchangeStaticPeSecDO> newStaticPeSecList = marketUpdateUtilMapper.selectNewExchangeStaticPeSecData(
            ifTodayLastTradingDayThisWeekAndMarketDataUpdated
    );
}
````
</augment_code_snippet>

**智能特性**：
- 🧠 **智能判断**：自动判断是否为周末最后交易日
- 📈 **数据更新**：只在合适的时机更新数据
- 🔍 **变化检测**：自动检测市盈率规则变化

---

## 🤝 Peer 模块 - 同业数据分析专家

### 🎯 模块概述
Peer模块是系统中最复杂的模块之一，专门处理同业券商数据的收集、清洗、分析和对比。就像一个专业的行业分析师，能够从海量的同业数据中提取有价值的信息！🔍📊

### 🔥 核心功能

#### 1. 智能数据修复 🔧
**功能描述**：自动识别和修复同业数据中的错误

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/peer/service/impl/PeerDataService.java" mode="EXCERPT">
````java
public void fixPeerTableData() {
    // 给同业券商表结构添加 enable_status 字段
    updateRealUtil.executeUpdateMethodOneParam(this::addPeerTableDesignateField,
            PeerConsts.TABLE_FIELD_ENABLE_STATUS);
    
    // 修复同业表市场字段为空的数据
    updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldNullValue, 
            "fixPeerTableMarketFieldNullValue");
    
    // 修复同业券商的market字段值错误的记录
    updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldErrorRecord,
            "fixPeerTableMarketFieldErrorRecord");
}
````
</augment_code_snippet>

**修复策略**：
- 🏗️ **结构修复**：自动添加缺失的表字段
- 🔍 **数据清洗**：识别和修复空值、错误值
- 🧠 **智能匹配**：基于证券名称智能匹配市场信息

#### 2. 并行数据处理 🚀
**功能描述**：高效处理大量同业证券数据

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/peer/service/impl/PeerDataService.java" mode="EXCERPT">
````java
private void updateDateListPeerSecData(List<String> tradingDateList) {
    List<PeerSecuritiesSetting> peerSecuritiesSettings =
            peerSecuritiesSettingService.list(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                    .eq(PeerSecuritiesSetting::getIfColumnShow, CommonStatusEnum.ENABLE));

    // 并行处理每个交易日的数据
    ParallelExecutorUtil.executeTasks(tasks, null, true);
    
    // 更新平均值和统计数据
    peerDataMapper.updatePeerAverageHaircutData(tradingDate);
    peerDataMapper.updatePeerAverageFinanceTargetData(tradingDate);
    peerDataMapper.updatePeerAverageShortSellTargetData(tradingDate);
}
````
</augment_code_snippet>

**性能优化**：
- ⚡ **并行处理**：多线程并行处理提升效率
- 📊 **批量操作**：减少数据库交互次数
- 🎯 **精准更新**：只更新变化的数据

---

## 💰 Margin 模块 - 融资融券业务专家

### 🎯 模块概述
Margin模块专门处理融资融券相关的业务数据，包括外规阈值监控、证券参数计算等。就像一个专业的风控专家，时刻监控着融资融券业务的风险指标！⚖️📊

### 🔥 核心功能

#### 1. 外规阈值监控 📊
**功能描述**：监控交易所外规业务参数阈值变化

<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/margin/business/controller/MrgTrdBusinessController.java" mode="EXCERPT">
````java
@Operation(summary = "自动更新交易所外规阈值对应的证券具体值")
@GetMapping("/autoUpdateExchangeRuleThresholdSecData")
@PassToken
@Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟执行一次
@SingleInstanceLock
public void autoUpdateExchangeRuleThresholdSecData() {
    if (!activeProfile.equals(ProfileTypeEnum.PROD.getCode())) {
        return;
    }
    
    exchangeRuleThresholdSecService.autoUpdateExchangeRuleThresholdSecData();
}
````
</augment_code_snippet>

**监控内容**：
- 📈 **市盈率阈值**：静态市盈率规则阈值
- 💎 **折算率限制**：担保品折算率上限
- ⚖️ **风险参数**：各类风险控制参数

### 🗃️ 数据模型

#### 外规配置实体
<augment_code_snippet path="modify-service/src/main/java/com/tjsj/modify/modules/margin/business/model/entity/ExchangeRuleThresholdConfigDO.java" mode="EXCERPT">
````java
@Schema(description = "交易所外规对于业务参数的阈值规约配置")
@Data
@TableName(value = "margin.t_exchange_rule_threshold_config")
public class ExchangeRuleThresholdConfigDO implements Serializable {
    
    @TableField(value = "mrd_trd_type")
    @Schema(description = "融资融券业务类型")
    private MrgTrdDataType mrdTrdType;
    
    @TableField(value = "sec_type")
    @Schema(description = "证券类型")
    private SecTypeEnum secType;
    
    @TableField(value = "threshold_value")
    @Schema(description = "阈值")
    private BigDecimal thresholdValue;
}
````
</augment_code_snippet>

---

## 🛠️ Common 模块 - 公共工具箱

### 🎯 模块概述
Common模块提供了系统中各个模块共用的工具类、常量定义、枚举类型等。就像一个万能工具箱，为其他模块提供强大的支持！🧰✨

### 🔧 核心组件

#### 1. 常量定义
- **PeerConsts**：同业相关常量
- **RedisConsts**：Redis键名常量
- **ManageConsts**：管理相关常量

#### 2. 枚举类型
- **MrgTrdDataType**：融资融券数据类型
- **SecTypeEnum**：证券类型枚举
- **CommonStatusEnum**：通用状态枚举

#### 3. 工具类
- **UpdateRealUtil**：数据更新工具
- **ParallelExecutorUtil**：并行执行工具
- **ModifyDataUtil**：数据修改工具

---

> 💡 **模块总结**：每个模块都像一个专业的工匠，在自己的领域内精益求精，共同构建了这个强大的金融数据处理系统！🏗️✨

下一步：查看 [代码质量评估](./04-代码质量评估.md) 了解项目的代码质量情况！
